package com.adins.mss.services.apimobile;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssRequestType;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.mss.businesslogic.api.am.ChangePasswordLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.common.AbsensiLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.LocationHistoryUserLogic;
import com.adins.mss.businesslogic.api.common.LoginUserLogic;
import com.adins.mss.businesslogic.api.common.PushNotificationLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.services.api.mobile.common.UserService;
import com.adins.mss.services.model.collection.EmergencyRequest;
import com.adins.mss.services.model.collection.EmergencyResponse;
import com.adins.mss.services.model.common.AbsensiInfoBean;
import com.adins.mss.services.model.common.AbsensiRequest;
import com.adins.mss.services.model.common.AbsensiResponse;
import com.adins.mss.services.model.common.ChangePasswordRequest;
import com.adins.mss.services.model.common.ChangePasswordResponse;
import com.adins.mss.services.model.common.CheckUpdateResponse;
import com.adins.mss.services.model.common.DetailPointRequest;
import com.adins.mss.services.model.common.DetailPointResponse;
import com.adins.mss.services.model.common.GetDetailKompetisiMobileRequest;
import com.adins.mss.services.model.common.GetDetailKompetisiMobileResponse;
import com.adins.mss.services.model.common.GetIconKompetisiMobileRequest;
import com.adins.mss.services.model.common.GetIconKompetisiMobileResponse;
import com.adins.mss.services.model.common.GetListKompetisiResponse;
import com.adins.mss.services.model.common.LocationHistoryUserRequest;
import com.adins.mss.services.model.common.LocationHistoryUserResponse;
import com.adins.mss.services.model.common.LoginMultiUserBean;
import com.adins.mss.services.model.common.LoginUserBean;
import com.adins.mss.services.model.common.LoginUserRequest;
import com.adins.mss.services.model.common.LoginUserResponse;
import com.adins.mss.services.model.common.MenuUserBean;
import com.adins.mss.services.model.common.UpdateFcmTokenRequest;
import com.adins.mss.services.model.common.UpdateFcmTokenResponse;
import com.adins.mss.services.model.common.UpdateUserAvatarRequest;
import com.adins.mss.services.model.common.UpdateUserAvatarResponse;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

@ApplicationScoped
@Path("user")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@SuppressWarnings({ "unchecked", "rawtypes" })
public class GenericUserServiceEndpoint implements UserService {	
	
	@Inject
	LoginUserLogic loginUserLogic;
	
	@Inject
	ChangePasswordLogic changePasswordLogic;
	
	@Inject
	LocationHistoryUserLogic locationHistoryUserLogic;
	
	@Inject
	AbsensiLogic absensiLogic;
	
	@Inject
	GlobalLogic globalLogic;
	
	@Inject
	PushNotificationLogic pushNotificationLogic;
	
	@Inject
	CommonLogic commonLogic;
	
	@Inject
	IntFormLogic intFormLogic;
	
	private final Gson gson = new GsonBuilder().create();
	
	@Override
	@POST
	@Path("multilogin")
	public String multiLogin(String request) {
		LoginUserRequest loginRequest = gson.fromJson(request, LoginUserRequest.class);
		List<LoginMultiUserBean> listLoginId = Collections.<LoginMultiUserBean> emptyList();
		AuditContext auditContext = loginRequest.getAudit().toAuditContext();
		
		listLoginId = loginUserLogic.doMobileMultiLogin(loginRequest.getUsername(), loginRequest.getPassword(), auditContext);
		
		LoginUserResponse loginResponse = new LoginUserResponse();
		loginResponse.setListLoginId(listLoginId);
		return gson.toJson(loginResponse, LoginUserResponse.class);
	}
	
	@Override
	@POST
	@Path("login")
	public String login(String request) {
		LoginUserRequest loginRequest = gson.fromJson(request, LoginUserRequest.class);
		String imei = null;
		String androidId = null;
		AuditContext auditContext = loginRequest.getAudit().toAuditContext();
		auditContext.setCallerId(loginRequest.getUsername());
		
		//get IMEI mobile
		for (int i = 0; i < loginRequest.getUnstructured().length; i++) {
			if ("imei".equals(loginRequest.getUnstructured()[i].getKey())) {
				imei = loginRequest.getUnstructured()[i].getValue();
			}
			if ("androidId".equals(loginRequest.getUnstructured()[i].getKey())) {
				androidId = loginRequest.getUnstructured()[i].getValue();
			}
		}
		//do mobile login
		AmMsuser loginBean = loginUserLogic.doMobileLogin(loginRequest.getUsername(),
				loginRequest.getPassword(), imei, androidId, loginRequest.getFlagFreshInstall(),
				loginRequest.getIsLogin(), loginRequest.getAudit(), auditContext);
		
		//set FCM Token ID
		pushNotificationLogic.saveTokenId(loginBean.getUuidMsUser(), loginRequest.getFcmTokenId(), auditContext);
		
		auditContext = loginRequest.getAudit().toAuditContext(); //re-create obj for enabling multitenant AOP
        auditContext.setCallerId(loginRequest.getUsername());
        Map<String, String>listUser = loginUserLogic.listLoginUserByUuid(loginBean.getUuidMsUser(), auditContext);
        
        DateFormat format = new SimpleDateFormat("ddMMyyyyHHmmss");
        if(null!=loginBean.getLastLoggedIn()) {
        	String formatted_date_last_logged_in = format.format(loginBean.getLastLoggedIn());
	 		listUser.put("last_logged_in", formatted_date_last_logged_in);
 	    } else {
 	    	listUser.put("last_logged_in", null);
 	    }
        if (null!=loginBean.getLastLoggedFail()) {
        	String formatted_date_last_logged_fail = format.format(loginBean.getLastLoggedFail());
			listUser.put("last_logged_fail", formatted_date_last_logged_fail);
 	    } else {
 	    	listUser.put("last_logged_fail", null);		
 	    }
		
		auditContext = loginRequest.getAudit().toAuditContext(); //re-create obj for enabling multitenant AOP
        auditContext.setCallerId(loginRequest.getUsername());
        List<LoginUserBean> listGs = loginUserLogic.listGeneralSetting(loginRequest.getAudit().getApplication(), auditContext);
		
		auditContext = loginRequest.getAudit().toAuditContext(); //re-create obj for enabling multitenant AOP
        auditContext.setCallerId(loginRequest.getUsername());
        List<MenuUserBean> listMenu = loginUserLogic.listMenu(loginBean, auditContext);
        
        AbsensiInfoBean checkedIn = loginUserLogic.getAttendanceToday(loginBean, GlobalVal.ATTENDANCE_IN, auditContext);
		AbsensiInfoBean checkedOut = loginUserLogic.getAttendanceToday(loginBean, GlobalVal.ATTENDANCE_OUT, auditContext);

		//set isExpiredNotification
		String isExpiredDate = loginUserLogic.isPwdExpiredNotification(loginBean.getUuidMsUser());
		listUser.put("password_expired_date", isExpiredDate);

		LoginUserResponse loginResponse = new LoginUserResponse();
		loginResponse.setListGeneralParameter(listGs);
		loginResponse.setUser(listUser);
		loginResponse.setListMenu(listMenu);
		loginResponse.setCheckedIn(checkedIn);
		loginResponse.setCheckedOut(checkedOut);
		auditContext.setCallerId(loginBean.getUuidMsUser().toString()); //fill callerid with uuid, because saveLatestActivityAttribute method need uuid value
		this.saveLatestActivityAttribute(auditContext);
		
		auditContext = loginRequest.getAudit().toAuditContext(); //re-create obj for enabling multitenant AOP

		if (StringUtils.isEmpty(auditContext.getCallerId())) {
			auditContext.setCallerId(loginRequest.getUsername()); //fill callerid with username, because  callerId empty on first Login
		}
        loginUserLogic.sendMessageToJms(loginBean, auditContext);
        
		return gson.toJson(loginResponse, LoginUserResponse.class);
	}

	@Override
	@POST
	@Path("changepassword")
	public String changePassword(String request) {
		ChangePasswordRequest changePasswordRequest = gson.fromJson(request, ChangePasswordRequest.class);

		String uuidMsUser = changePasswordRequest.getUuidMsUser();
		String oldPassword = changePasswordRequest.getOldPassword();
		String newPassword = changePasswordRequest.getNewPassword();
		AuditContext callerId = changePasswordRequest.getAudit().toAuditContext();
		AmMsuser changePassword = changePasswordLogic.changePassword(NumberUtils.toLong(uuidMsUser), oldPassword, newPassword, callerId);

		ChangePasswordResponse changePasswordResponse = new ChangePasswordResponse();
		changePasswordResponse.setChangePassword(String.valueOf(changePassword.getUuidMsUser()));
		this.saveLatestActivityAttribute(callerId);
		
		return gson.toJson(changePasswordResponse, ChangePasswordResponse.class);
	}
	
	@Override
	@POST
	@Path("updatelocationhistory")
	public String updateLocationHistory(String request) throws ParseException {
		LocationHistoryUserRequest locationHistoryUserRequest = gson.fromJson(request, LocationHistoryUserRequest.class);
		AuditContext callerId = locationHistoryUserRequest.getAudit().toAuditContext();
		String surveyor = locationHistoryUserLogic.saveLocationHistory(locationHistoryUserRequest.getListLocationInfo(),
				locationHistoryUserRequest.getPrecentageBattery(), locationHistoryUserRequest.getDataUsage(),callerId);
		callerId = locationHistoryUserRequest.getAudit().toAuditContext(); //re-create obj for enabling multitenant AOP
		locationHistoryUserLogic.sendMessageToJms(locationHistoryUserRequest.getListLocationInfo(), callerId);
		
		LocationHistoryUserResponse locationHistoryUserResponse = new LocationHistoryUserResponse();
		locationHistoryUserResponse.setSurveyor(surveyor);
		
		return gson.toJson(locationHistoryUserResponse, LocationHistoryUserResponse.class);
	}
	
	
	@Override
	@POST
	@Path("absensi")
	public String doAbsensi(String request) {
		AbsensiRequest absensiReq = gson.fromJson(request, AbsensiRequest.class);

		AuditContext callerId = absensiReq.getAudit().toAuditContext();
		
		Map<String, String> result = absensiLogic.doAbsensi(absensiReq.getLocationInfo(),absensiReq.getAttd_address(),callerId, absensiReq.getFlagAttd());
		
		AbsensiResponse absensiRes = new AbsensiResponse();
		absensiRes.setResult(result.get(GlobalVal.KEY_DOABSENSI_MESSAGERESULT));
		absensiRes.setSuccess(result.get(GlobalVal.KEY_DOABSENSI_SUCCESS));
		this.saveLatestActivityAttribute(callerId);
		
		callerId = absensiReq.getAudit().toAuditContext(); //re-create obj for enabling multitenant AOP
		absensiLogic.sendMessageToJms(absensiReq.getLocationInfo(), result.get(GlobalVal.KEY_DOABSENSI_MESSAGEKEY), callerId);

		return gson.toJson(absensiRes, AbsensiResponse.class);
	}
	
	@Override
	@POST
	@Path("checkupdate")
	public String checkUpdate(String request) {
		MssRequestType req = gson.fromJson(request, MssRequestType.class);

		AuditContext callerId = req.getAudit().toAuditContext();
		List<Map<String, String>> listMap = new ArrayList<>();
		String subsystem = req.getAudit().getApplication();
		
		String gsVal = globalLogic.getGsValue(subsystem+GlobalKey.GENERALSETTING_LAST_MOBILE_VERSION, callerId);
		Map<String, String> mapVal = new HashMap<>();
		mapVal.put("key", GlobalKey.GENERALSETTING_LAST_MOBILE_VERSION.substring(1));
		mapVal.put("value", gsVal);
		listMap.add(mapVal);
		
		String link = globalLogic.getGsValue(subsystem+GlobalKey.GENERALSETTING_OTA_DOWNLOAD_LINK, callerId);
		Map<String, String> mapLink = new HashMap<>();
		mapLink.put("key", GlobalKey.GENERALSETTING_OTA_DOWNLOAD_LINK.substring(1));
		mapLink.put("value", link);
		listMap.add(mapLink);
		
		CheckUpdateResponse res = new CheckUpdateResponse();
		res.setListValue(listMap);
		
		return gson.toJson(res, CheckUpdateResponse.class);
	}
	
	@Override
	@POST
	@Path("updatefcm")
	public String updateFCM(String request) {
		UpdateFcmTokenRequest req = gson.fromJson(request, UpdateFcmTokenRequest.class);
		UpdateFcmTokenResponse resp = new UpdateFcmTokenResponse();

		AuditContext auditContext = req.getAudit().toAuditContext();
		
		if (StringUtils.isNotBlank(req.getFcmTokenId())) {
			pushNotificationLogic.saveTokenId(NumberUtils.toLong(req.getUuid_user()), req.getFcmTokenId(), auditContext);
			resp.setFcmTokenId(req.getFcmTokenId());
		} else {
			resp.setFcmTokenId(req.getFcmTokenId());
		}
		
		return gson.toJson(resp, UpdateFcmTokenResponse.class);
	}
	
	@Override
	@POST
	@Path("updateavatar")
	public String updateUserAvatar(String request) {
		UpdateUserAvatarRequest req = gson.fromJson(request, UpdateUserAvatarRequest.class);
		UpdateUserAvatarResponse resp = new UpdateUserAvatarResponse();
		
		AuditContext auditContext = req.getAudit().toAuditContext();
		
		String update = loginUserLogic.updateUserAvatar(req.getUuid_user(), req.getImage(), auditContext);
		resp.setResult(update);
		
		return gson.toJson(resp, UpdateUserAvatarResponse.class);
	}
	
	private void saveLatestActivityAttribute(AuditContext callerId) {
    	if (callerId == null) {
        	return;
        }
    	
        this.commonLogic.insertLastActivityTimestamp(new Date(), callerId);
    }
    
    public IntFormLogic getIntFormLogic() {
        return intFormLogic;
    }
	
	@Override
	@POST
	@Path("emergencynotification")
	public String emergencyNotification(String request) {
		EmergencyRequest emergencyRequest = gson.fromJson(request, EmergencyRequest.class);
		EmergencyResponse emergencyResponse = new EmergencyResponse();
		
		AuditContext callerId = new AuditContext(emergencyRequest.getAudit().getCallerId());					
		String uuidEmergency = loginUserLogic.insertEmergency(emergencyRequest.getDtmEmergency(), emergencyRequest.getLatitude(), 
				emergencyRequest.getLongitude() ,callerId);
		
		callerId = emergencyRequest.getAudit().toAuditContext(); //re-create obj for enabling multitenant AOP
		Status status = new Status();
		if (StringUtils.isNotEmpty(uuidEmergency)) {
			loginUserLogic.sendMessageToJms(emergencyRequest.getDtmEmergency(), 
					emergencyRequest.getLatitude(), emergencyRequest.getLongitude(), uuidEmergency, callerId);
			status.setCode(0);
			status.setMessage("OK");
		} else {
			status.setCode(1);
			status.setMessage("FAILED");
		}
		emergencyResponse.setStatus(status);
		
		return gson.toJson(emergencyResponse);
	}
	
	@Override
	@POST
	@Path("detailpoint")
	public String detailPoint(String request) {
		DetailPointRequest detailPointRequest = gson.fromJson(request, DetailPointRequest.class);
		Status status = new Status();
		AuditContext callerId = new AuditContext(detailPointRequest.getAudit().getCallerId());	
		if (StringUtils.isBlank(detailPointRequest.getLoginId()) || 
				StringUtils.isBlank(detailPointRequest.getMembershipProgramCode())) {
			status.setCode(1);
			status.setMessage("Try again later or re-sync to open your details achievement");
			DetailPointResponse resp = new DetailPointResponse();
			resp.setStatus(status);
			
			return gson.toJson(resp);
		}
		
		// Get From Staging
		DetailPointResponse detailPointResponse = this.getIntFormLogic().getDetailPoint(detailPointRequest, callerId);
		detailPointResponse = this.getIntFormLogic().changeLevelPointLoyalti(detailPointResponse, callerId);
		
		status.setCode(0);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		detailPointResponse.setStatus(status);
		
		return gson.toJson(detailPointResponse);
	}
	
	@Override
	@POST
	@Path("getdetailcompetition")
	public String getDetailKompetisi(String request) throws ParseException {
		GetDetailKompetisiMobileRequest getKompetisiMobileRequest = gson.fromJson(request, GetDetailKompetisiMobileRequest.class);
		
		AuditContext callerId = getKompetisiMobileRequest.getAudit().toAuditContext();
		GetListKompetisiResponse competition = this.getIntFormLogic().getListKompetisi(getKompetisiMobileRequest.getLoginId(), callerId);
		callerId = getKompetisiMobileRequest.getAudit().toAuditContext();
		
		Status status = new Status();
		GetDetailKompetisiMobileResponse getDetailKompetisiMobileResponse = new GetDetailKompetisiMobileResponse();
		if (competition.getListCompetition().isEmpty()) {
			status.setCode(1);
			status.setMessage("No competition found");
		} else {
			getDetailKompetisiMobileResponse = this.getIntFormLogic().setRankOrPoint(competition, callerId);
			status.setCode(0);
			status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		}
		getDetailKompetisiMobileResponse.setStatus(status);
		
		return gson.toJson(getDetailKompetisiMobileResponse, GetDetailKompetisiMobileResponse.class);
	}
	
	@Override
	@POST
	@Path("geticoncompetition")
	public String getIconKompetisi(String request) {
		GetIconKompetisiMobileRequest getIconRequest = gson.fromJson(request, GetIconKompetisiMobileRequest.class);
		AuditContext callerId = getIconRequest.getAudit().toAuditContext();
		String icon = this.getIntFormLogic().getIconKompetisi(getIconRequest.getMembershipProgramCode(), callerId);
		
		GetIconKompetisiMobileResponse getIconResponse = new GetIconKompetisiMobileResponse();
		getIconResponse.setLogo(icon);
		
		Status status = new Status();
		status.setCode(0);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		getIconResponse.setStatus(status);
		
		return gson.toJson(getIconResponse, GetIconKompetisiMobileResponse.class);
	}
	
}